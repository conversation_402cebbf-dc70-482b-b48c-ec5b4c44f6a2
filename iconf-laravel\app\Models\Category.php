<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use SolutionForest\FilamentTree\Concern\ModelTree;

/**
 * 
 *
 * @property int $id
 * @property int $listorder
 * @property int $fid 父分类 0 表示一级分类，分类层级共两级
 * @property string $name 分类名称
 * @property string $url 网址
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Advertisement> $advertisements
 * @property-read int|null $advertisements_count
 * @property-read array $breadcrumb
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Category> $children
 * @property-read int|null $children_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Event> $events
 * @property-read int|null $events_count
 * @property-read string $full_url
 * @property-read bool $has_children
 * @property-read bool $is_top_level
 * @property-read Category|null $parent
 * @property-read string $sortable_path
 * @method static \Database\Factories\CategoryFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category isRoot()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category ordered(string $direction = 'asc')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category subCategories()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category topLevel()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereFid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereListorder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereUrl($value)
 * @mixin \Eloquent
 */
class Category extends Model
{
    use HasFactory;
    use ModelTree;
    /**
     * 指定表名
     */
    protected $table = 'category';

    /**
     * 禁用Laravel的时间戳字段
     */
    public $timestamps = false;

    /**
     * 可批量赋值的字段
     */
    protected $fillable = [
        'fid',
        'name',
        'url',
        'listorder',
    ];

    /**
     * 字段类型转换
     */
    protected $casts = [
        'fid' => 'integer',
        'listorder' => 'integer',
    ];

    /**
     * 关联：父分类
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'fid');
    }

    /**
     * 关联：子分类
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'fid')->orderBy('listorder');
    }

    /**
     * 关联：分类下的会议（多对多）
     */
    public function events(): BelongsToMany
    {
        return $this->belongsToMany(
            Event::class,
            'list',  // 中间表名
            'cid',   // 当前模型在中间表的外键
            'eid'    // 关联模型在中间表的外键
        )->select('event.*'); // 明确选择event表的字段，避免id字段冲突
    }

    /**
     * 关联：分类下的广告
     */
    public function advertisements(): HasMany
    {
        return $this->hasMany(Advertisement::class, 'cid');
    }



    /**
     * 作用域：顶级分类
     */
    public function scopeTopLevel($query)
    {
        return $query->where('fid', 0);
    }

    /**
     * 作用域：子分类
     */
    public function scopeSubCategories($query)
    {
        return $query->where('fid', '>', 0);
    }

    /**
     * 访问器：是否为顶级分类
     */
    public function isTopLevel(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => $this->fid === 0
        );
    }

    /**
     * 访问器：是否有子分类
     */
    public function hasChildren(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => $this->children()->count() > 0
        );
    }

    /**
     * 访问器：分类层级路径
     */
    public function breadcrumb(): Attribute
    {
        return Attribute::make(
            get: function (): array {
                $breadcrumb = [$this];
                $parent = $this->parent;

                while ($parent) {
                    array_unshift($breadcrumb, $parent);
                    $parent = $parent->parent;
                }

                return $breadcrumb;
            }
        );
    }

    /**
     * 获取分类的完整URL路径
     */
    public function fullUrl(): Attribute
    {
        return Attribute::make(
            get: function (): string {
                $urls = collect($this->breadcrumb)->pluck('url')->filter();
                return $urls->implode('/');
            }
        );
    }
    
    /**
     * 为 filament-tree 插件定义父级字段名
     */
    public function determineParentColumnName(): string
    {
        return 'fid';
    }
    
    /**
     * 为 filament-tree 插件定义排序字段名
     */
    public function determineOrderColumnName(): string
    {
        return 'listorder';
    }
    
    /**
     * 为 filament-tree 插件定义标题字段名
     */
    public function determineTitleColumnName(): string
    {
        return 'name';
    }
    
    /**
     * 为 filament-tree 插件定义默认父级值
     */
    public static function defaultParentKey()
    {
        return 0; // 我们使用0作为顶级分类的标识，而不是插件默认的-1
    }

    /**
     * 访问器：用于表格排序的层级路径
     */
    protected function sortablePath(): Attribute
    {
        return Attribute::make(
            get: function (): string {
                $path = [];
                $current = $this;
                while ($current) {
                    // 使用 listorder 和 id 组合，确保唯一性和排序稳定性
                    $path[] = str_pad($current->listorder, 3, '0', STR_PAD_LEFT) . '-' . str_pad($current->id, 5, '0', STR_PAD_LEFT);
                    $current = $current->parent;
                }
                return implode('-', array_reverse($path));
            }
        );
    }
}
