<?php

namespace App\View\Components;

use App\Models\Link;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class FriendlyLinks extends Component
{
    /**
     * 友情链接数据
     */
    public $links;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        // 获取启用的友情链接，按排序字段排序
        $this->links = Link::active()->ordered()->get();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.friendly-links');
    }
}
