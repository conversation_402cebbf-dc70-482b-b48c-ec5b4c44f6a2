<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NewsTypeResource\Pages;
use App\Models\NewsType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class NewsTypeResource extends Resource
{
    protected static ?string $model = NewsType::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationLabel = '新闻分类';

    protected static ?string $modelLabel = '新闻分类';

    protected static ?string $pluralModelLabel = '新闻分类';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationGroup = '内容管理';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('分类信息')
                    ->schema([
                        Forms\Components\TextInput::make('column_name')
                            ->label('分类名称')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('新闻分类的名称，必须唯一'),

                        Forms\Components\TextInput::make('column_sort')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->helperText('数字越小排序越靠前，相同数字按ID排序'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('column_name')
                    ->label('分类名称')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('column_sort')
                    ->label('排序')
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('news_count')
                    ->label('新闻数量')
                    ->getStateUsing(fn ($record) => $record->getNewsCount())
                    ->alignCenter()
                    ->badge()
                    ->color('success'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (NewsType $record) {
                        // 删除前检查是否有关联的新闻
                        if ($record->getNewsCount() > 0) {
                            throw new \Exception('该分类下还有新闻，请先删除或移动新闻到其他分类');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                if ($record->getNewsCount() > 0) {
                                    throw new \Exception("分类「{$record->column_name}」下还有新闻，请先删除或移动新闻到其他分类");
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('column_sort', 'asc')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNewsTypes::route('/'),
            'create' => Pages\CreateNewsType::route('/create'),
            'edit' => Pages\EditNewsType::route('/{record}/edit'),
        ];
    }

    /**
     * 获取导航徽章（显示分类总数）
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * 获取导航徽章颜色
     */
    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
